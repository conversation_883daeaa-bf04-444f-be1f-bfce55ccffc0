# GazeNeRF: 基于神经辐射场的3D感知视线重定向

本仓库是CVPR 2023论文的官方PyTorch实现\
[“GazeNeRF: 3D-Aware Gaze Redirection with Neural Radiance Fields”](https://arxiv.org/abs/2212.04823)

- 作者: [<PERSON>*](https://alessandroruzzi.github.io), [<PERSON><PERSON><PERSON>*](), [<PERSON>](https://ait.ethz.ch/people/xiwang), [<PERSON><PERSON><PERSON>](https://ait.ethz.ch/people/lig), [<PERSON><PERSON><PERSON>](https://research.nvidia.com/person/shalini-de-mello), [<PERSON><PERSON>](https://www.birmingham.ac.uk/staff/profiles/computer-science/academic-staff/chang-jin-hyung.aspx), [Xucong Zhang](https://cvlab-tudelft.github.io/people/xucong-zhang/), [<PERSON><PERSON><PERSON>](https://ait.ethz.ch/people/hilliges)
- [项目主页](https://x-shi.github.io/GazeNeRF.github.io/)

https://github.com/AlessandroRuzzi/GazeNeRF/assets/76208014/7607b8fc-2aa5-45fc-8f0f-542d9dab9597

## 环境要求
论文中的模型使用Python 3.8.8、PyTorch 1.12.0、CUDA 11.3和CentOS 7.9.2009进行训练。

安装所需包，运行:\
`pip install -r requirements.txt`

安装数据预处理所需的库，请参考[此仓库](https://github.com/CrisHY1995/headnerf/tree/main)。

## 数据集
我们的模型使用ETH-XGaze数据集进行训练，并在ETH-XGaze、Columbia、MPIIFaceGaze和GazeCapture数据集上进行评估。预处理代码主要基于[数据标准化](https://github.com/xucong-zhang/data-preprocessing-gaze)仓库、[HeadNeRF](https://github.com/CrisHY1995/headnerf/tree/main)仓库和[此仓库](https://github.com/switchablenorms/CelebAMask-HQ)。

预处理数据集，运行:\
`python dataset_pre_precessing.py --dataset_dir=/path/to/your/dataset --dataset_name=eth_xgaze --output_dir=/path/to/your/output/directory`

- 我们不提供数据集下载链接。

## 训练
训练GazeNeRF模型，运行\
`python train.py --batch_size=2 --log=true --learning_rate=0.0001 --img_dir='/path/to/your/ETH-XGaze/training/dataset'`

我们的模型在单个NVIDIA A40 GPU上进行训练。

## 评估
评估训练好的模型，运行\
`python evaluate_metrics.py --log=true --num_epochs=75 --model_path=checkpoints/your_checkpoints.json`

生成插值演示，运行\
`python evaluate.py --model_path=checkpoints/your_checkpoints.json --img_dir='/path/to/your/ETH-XGaze/test/dataset'`

## 预训练模型
您可以在[这里](https://drive.google.com/file/d/100ksmOoWc5kFB0V4eT0RZecI9N1Hr2vu/view?usp=sharing)下载预训练的GazeNeRF模型，在[这里](https://drive.google.com/file/d/1YFQjLYx187XyhGj6SGEONmgV3lBieJsn/view?usp=share_link)下载视线估计器。

## 引用

```
@InProceedings{ruzzi2023gazenerf,
    author    = {Ruzzi, Alessandro and Shi, Xiangwei and Wang, Xi and Li, Gengyan and De Mello, Shalini and Chang, Hyung Jin and Zhang, Xucong and Hilliges, Otmar},
    title     = {GazeNeRF: 3D-Aware Gaze Redirection with Neural Radiance Fields},
    booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
    year      = {2023},
    pages     = {9676--9685}
}
```

