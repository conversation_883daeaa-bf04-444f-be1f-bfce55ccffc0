#!/usr/bin/env python3
"""
自定义视线控制脚本
允许用户指定具体的视线方向进行重定向
"""

import torch
import argparse
from utils.vis_utils import GazeNeRFUtils

def parse_args():
    parser = argparse.ArgumentParser(description="自定义视线重定向")
    parser.add_argument("--model_path", type=str, 
                       default="checkpoints/Pretrained_GazeNeRF.json",
                       help="预训练模型路径")
    parser.add_argument("--input_image", type=str, required=True,
                       help="输入图像路径")
    parser.add_argument("--output_dir", type=str, default="output",
                       help="输出目录")
    parser.add_argument("--pitch", type=float, default=0.0,
                       help="上下视线角度 (-0.5 到 0.5)")
    parser.add_argument("--yaw", type=float, default=0.0,
                       help="左右视线角度 (-0.6 到 0.6)")
    parser.add_argument("--preset", type=str, choices=[
                       "up", "down", "left", "right", "up_left", "down_right"
                       ], help="预设视线方向")
    return parser.parse_args()

def get_preset_gaze(preset):
    """获取预设的视线方向"""
    presets = {
        "up": [-0.3, 0.0],
        "down": [0.3, 0.0], 
        "left": [0.0, -0.4],
        "right": [0.0, 0.4],
        "up_left": [-0.2, -0.3],
        "down_right": [0.2, 0.3]
    }
    return presets.get(preset, [0.0, 0.0])

def main():
    args = parse_args()
    
    print("🎯 GazeNeRF 自定义视线重定向")
    print("=" * 50)
    
    # 确定视线方向
    if args.preset:
        pitch, yaw = get_preset_gaze(args.preset)
        print(f"📐 使用预设方向: {args.preset}")
    else:
        pitch, yaw = args.pitch, args.yaw
        print(f"📐 使用自定义角度")
    
    print(f"   Pitch (上下): {pitch:+.2f}")
    print(f"   Yaw (左右): {yaw:+.2f}")
    
    try:
        # 初始化GazeNeRF工具
        print(f"\n🔍 加载模型: {args.model_path}")
        gaze_utils = GazeNeRFUtils(args.model_path)
        
        print(f"📷 处理图像: {args.input_image}")
        
        # 这里您可以添加实际的图像处理代码
        # 由于需要完整的数据预处理流程，这里只是示例框架
        
        print(f"💾 输出将保存到: {args.output_dir}")
        print("✅ 脚本框架运行成功！")
        print("\n💡 提示: 要进行实际的图像处理，您需要:")
        print("   1. 准备预处理的数据集")
        print("   2. 使用 evaluate.py 进行完整的推理")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
