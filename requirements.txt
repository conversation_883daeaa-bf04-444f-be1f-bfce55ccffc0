# 兼容版本的requirements.txt
charset-normalizer>=2.0.12
# cmake  # 已通过conda安装
# dlib  # 已通过conda安装
# face-alignment  # 稍后单独安装
h5py
idna>=3.3
imageio>=2.18.0
imutils
kornia>=0.6.4
llvmlite>=0.38.0
networkx>=2.8
numba>=0.55.1
numpy
opencv-python>=4.3.0
packaging>=21.3
pandas
pillow>=9.1.1
piq
pyparsing>=3.0.8
# pyqt5==5.15.6  # 可能不需要GUI
# pyqt5-qt5==5.15.2
# pyqt5-sip==12.10.1
pywavelets>=1.3.0
requests>=2.27.1
scikit-image>=0.19.2
scipy>=1.8.0
sqlalchemy
tifffile>=2022.4.28
# torch  # 已安装
# torchvision  # 已安装
typing-extensions>=4.2.0
urllib3>=1.26.9
wandb
