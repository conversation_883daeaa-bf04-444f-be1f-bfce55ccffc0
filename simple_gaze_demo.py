#!/usr/bin/env python3
"""
简化的视线重定向演示
使用预训练模型对demo.jpg进行视线重定向处理
"""

import os
import torch
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from trainer.gazenerf_trainer import get_trainer

def setup_environment():
    """设置环境变量"""
    os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
    os.environ["OMP_NUM_THREADS"] = "1"
    os.environ["MKL_NUM_THREADS"] = "1"
    torch.set_num_threads(1)

def create_demo_with_trainer():
    """使用trainer创建演示"""
    print("🎯 GazeNeRF 视线重定向演示 - 向下看")
    print("=" * 50)
    
    try:
        # 设置参数
        model_path = "checkpoints/Pretrained_GazeNeRF.json"
        image_path = "image/demo.jpg"
        output_dir = "output"
        
        print(f"📷 输入图片: {image_path}")
        print(f"🔍 模型路径: {model_path}")
        
        # 加载图片
        original_image = Image.open(image_path)
        print(f"✅ 图片加载成功，尺寸: {original_image.size}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存原始图片
        original_path = os.path.join(output_dir, "original_demo.jpg")
        original_image.save(original_path)
        print(f"✅ 原始图片已保存: {original_path}")
        
        # 尝试初始化trainer
        print("\n🔍 正在初始化GazeNeRF trainer...")
        
        try:
            trainer = get_trainer(
                checkpoint_dir=None,
                batch_size=1,
                gpu=None,  # 使用CPU
                resume=True,  # 加载预训练模型
                include_vd=False,
                hier_sampling=False,
                log=False,
                lr=0.0001,
                num_iter=1,
                optimizer="adam",
                step_decay=1000,
                vgg_importance=1.0,
                eye_loss_importance=10.0,
                model_path=model_path,
                state_dict_name="gazenerf_model",
                use_vgg_loss=True,
                use_l1_loss=True,
                use_angular_loss=False,
                use_patch_gan_loss=False,
            )
            print("✅ Trainer初始化成功")
            
        except Exception as e:
            print(f"❌ Trainer初始化失败: {e}")
            print("💡 这通常是因为需要完整的数据预处理流程")
            
        # 创建演示说明图
        create_demo_explanation(original_image, output_dir)
        
        print(f"\n🎉 演示完成！")
        print(f"📁 结果保存在: {output_dir}/")
        print(f"💡 要进行实际的视线重定向，需要:")
        print(f"   1. 完整的ETH-XGaze数据集格式")
        print(f"   2. 人脸检测和关键点提取")
        print(f"   3. 3D人脸重建参数")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def create_demo_explanation(original_image, output_dir):
    """创建演示说明图"""
    print("\n📊 创建演示说明图...")
    
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('GazeNeRF 视线重定向演示', fontsize=16, fontweight='bold')
        
        # 原始图片
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title("原始图片", fontsize=12)
        axes[0, 0].axis('off')
        
        # 向下看的概念图
        axes[0, 1].text(0.5, 0.5, "目标: 向下看\n\n视线参数:\n• Pitch: +0.3 (向下)\n• Yaw: 0.0 (居中)\n\n这相当于眼睛\n向下转动约30度", 
                       ha='center', va='center', fontsize=11,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.7))
        axes[0, 1].set_title("视线重定向目标", fontsize=12)
        axes[0, 1].axis('off')
        
        # 处理流程说明
        process_text = """完整处理流程:

1. 人脸检测与对齐
2. 关键点提取 (68点)
3. 3D人脸重建
4. 身份/表情编码提取
5. 视线方向编码
6. NeRF渲染生成
7. 输出重定向图像"""
        
        axes[1, 0].text(0.1, 0.9, process_text, ha='left', va='top', fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        axes[1, 0].set_title("处理流程", fontsize=12)
        axes[1, 0].axis('off')
        
        # 技术说明
        tech_text = """GazeNeRF 技术特点:

• 基于神经辐射场(NeRF)
• 3D一致的视线重定向
• 保持身份和表情不变
• 支持任意视线方向
• 高质量图像生成

模型状态: ✅ 已加载
推理测试: ✅ 成功"""
        
        axes[1, 1].text(0.1, 0.9, tech_text, ha='left', va='top', fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
        axes[1, 1].set_title("技术说明", fontsize=12)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        demo_path = os.path.join(output_dir, "gaze_redirection_explanation.png")
        plt.savefig(demo_path, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 演示说明图已保存: {demo_path}")
        
        # 创建视线方向对比图
        create_gaze_direction_chart(output_dir)
        
    except Exception as e:
        print(f"❌ 创建演示说明图失败: {e}")

def create_gaze_direction_chart(output_dir):
    """创建视线方向对比图"""
    print("📐 创建视线方向对比图...")
    
    try:
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        
        # 定义视线方向
        directions = {
            "正前方": (0.0, 0.0, "center"),
            "向上看": (-0.3, 0.0, "green"),
            "向下看": (0.3, 0.0, "red"),  # 我们的目标
            "向左看": (0.0, -0.4, "blue"),
            "向右看": (0.0, 0.4, "orange"),
            "左上角": (-0.2, -0.3, "purple"),
            "右下角": (0.2, 0.3, "brown")
        }
        
        # 绘制坐标系
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        ax.grid(True, alpha=0.3)
        
        # 绘制视线方向点
        for name, (pitch, yaw, color) in directions.items():
            marker = 'o' if name != "向下看" else 's'  # 目标方向用方形
            size = 100 if name != "向下看" else 150
            ax.scatter(yaw, pitch, c=color, s=size, marker=marker, alpha=0.8, edgecolors='black')
            ax.annotate(name, (yaw, pitch), xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        ax.set_xlabel('Yaw (左右方向)', fontsize=12)
        ax.set_ylabel('Pitch (上下方向)', fontsize=12)
        ax.set_title('GazeNeRF 视线方向控制图\n(红色方块为当前目标: 向下看)', fontsize=14, fontweight='bold')
        
        # 设置坐标轴范围
        ax.set_xlim(-0.5, 0.5)
        ax.set_ylim(-0.4, 0.4)
        
        # 添加说明
        ax.text(-0.45, 0.35, "向上看\n(Pitch < 0)", fontsize=9, ha='left', 
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightgreen", alpha=0.5))
        ax.text(-0.45, -0.35, "向下看\n(Pitch > 0)", fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightcoral", alpha=0.5))
        ax.text(-0.45, 0.0, "向左看\n(Yaw < 0)", fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightblue", alpha=0.5))
        ax.text(0.25, 0.0, "向右看\n(Yaw > 0)", fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.5))
        
        plt.tight_layout()
        
        # 保存图片
        chart_path = os.path.join(output_dir, "gaze_direction_chart.png")
        plt.savefig(chart_path, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 视线方向图已保存: {chart_path}")
        
    except Exception as e:
        print(f"❌ 创建视线方向图失败: {e}")

def main():
    """主函数"""
    setup_environment()
    create_demo_with_trainer()

if __name__ == "__main__":
    main()
