[loggers] # loggers object list
    keys = root, sdk, api

[handlers] # handlers object list
    keys = consoleHandlers, fileHandlers

[formatters] # formatters list
    keys = fmt

[logger_root]
    level = DEBUG
    handlers = consoleHandlers, fileHandlers

[logger_sdk] # sdk logger
    level = DEBUG
    handlers = fileHandlers
    qualname = sdk
    propagate = 0

[logger_api] # api logger
    level = DEBUG
    handlers = consoleHandlers
    qualname = api
    propagate = 0

[handler_consoleHandlers]# consoleHandlers.
    class = StreamHandler
    level = DEBUG
    formatter = fmt
    args = (sys.stdout,)

[handler_fileHandlers]]# fileHandlers
    class = logging.handlers.RotatingFileHandler
    level = DEBUG
    formatter = fmt
    args = ('logs/sdk.log', 'a', 10000, 3, 'UTF-8')

[formatter_fmt] # fmt format
    format = %(levelname)s %(asctime)s %(filename)s: %(lineno)d] %(message)s
    datefmt = %Y-%m-%d %H:%M:%S